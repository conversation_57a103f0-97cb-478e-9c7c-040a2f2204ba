#include "pch.h"
#include "entity.hpp"
#include "gamevars.hpp"
#include "offsets.hpp"
#include <thread>
#include <chrono>
#include <iostream>
#include <string>

void Reader::ThreadPlayers() {
  // Wait for complete initialization
  while ( !GameVars::getInstance()->initialized() ) {
    std::this_thread::sleep_for( std::chrono::milliseconds( 100 ) );
  }

  auto vars = GameVars::getInstance();
  std::cout << "[+] Thread Initialization complete" << std::endl;
  //std::cout << "[Thread] Process ID: 0x" << std::hex << vars->getProcessId() << std::endl;
  //std::cout << "[Thread] Client: 0x" << std::hex << vars->getClient() << std::endl;
  //std::cout << "[Thread] Driver: 0x" << std::hex << vars->getDriver() << std::endl;

  while ( true ) {
    FilterPlayers();
    std::this_thread::sleep_for( std::chrono::milliseconds( 5 ) );
  }
}

void Reader::FilterPlayers() {
  std::vector<C_CSPlayerPawn> newPlayerList;

  auto* gameVars = GameVars::getInstance();
  HANDLE driverHandle = gameVars->getDriver();
  uintptr_t clientBase = gameVars->getClient();

  if ( !driverHandle ) {
    std::cout << "[-] Driver handle is null" << std::endl;
    return;
  }

  if ( !clientBase ) {
    std::cout << "[-] Client base is null" << std::endl;
    return;
  }

  uintptr_t entityList = driver::read_memory<uintptr_t>( driverHandle, clientBase + Offset::EntityList );

  uintptr_t localPawn = driver::read_memory<uintptr_t>( driverHandle, clientBase + Offset::LocalPlayerPawn );
  uintptr_t localPlayer = driver::read_memory<uintptr_t>( driverHandle, clientBase + Offset::LocalPlayerController );

  // Use a fixed stride to iterate the entity list.
  for ( int playerIndex = 0; playerIndex < 64; playerIndex++ ) {
    uintptr_t listentry  = driver::read_memory<uintptr_t>( driverHandle, entityList + ( ( 8 * ( playerIndex & 0x7FFF ) >> 9 ) + 16 ) );
    uintptr_t player     = driver::read_memory<uintptr_t>( driverHandle, listentry + 120 * ( playerIndex & 0x1FF ) );

    if ( player == localPlayer )
      continue;

    std::string PlayerName = driver::read_string( driverHandle, player + Offset::Entity::iszPlayerName );
    CCSPlayerPawn.PlayerName = PlayerName;

    uint32_t  playerPawn = driver::read_memory<uint32_t>( driverHandle, player + Offset::PlayerController::m_hPawn );

    if (!playerPawn)
      continue;

    uintptr_t listentry2       = driver::read_memory<uintptr_t>( driverHandle, entityList + 0x8 * ( ( playerPawn & 0x7FFF ) >> 9 ) + 16 );
    uintptr_t pCSPlayerPawnPtr = driver::read_memory<uintptr_t>( driverHandle, listentry2 + 120 * ( playerPawn & 0x1FF ) );
    CCSPlayerPawn.pCSPlayerPawn = pCSPlayerPawnPtr;
    CCSPlayerPawn.entityId = playerIndex;

    // Testing offsets:
    //std::cout << "m_pObserverServices: 0x" << std::hex << Offset::PlayerController::m_pObserverServices << std::endl;
    //std::cout << "m_hObserverTarget: 0x" << std::hex << Offset::PlayerController::m_hObserverTarget << std::endl;

    // Check if this player is spectating someone
    uintptr_t  m_pObserverServices = driver::read_memory<uintptr_t>(driverHandle, pCSPlayerPawnPtr + Offset::PlayerController::m_pObserverServices);

    if (m_pObserverServices)
    {
      uint32_t  m_hObserverTarget = driver::read_memory<uint32_t>(driverHandle, m_pObserverServices + Offset::PlayerController::m_hObserverTarget);

      if (m_hObserverTarget != 0)
      {
        // Get the observer target pawn from handle
        uintptr_t targetListEntry = driver::read_memory<uintptr_t>(driverHandle, entityList + 0x8 * ((m_hObserverTarget & 0x7FFF) >> 9) + 16);
        uintptr_t observer_target_pawn = driver::read_memory<uintptr_t>(driverHandle, targetListEntry + 120 * (m_hObserverTarget & 0x1FF));

        if (observer_target_pawn)
        {
          // Get the controller of the target pawn
          uintptr_t target_controller = driver::read_memory<uintptr_t>(driverHandle, observer_target_pawn + Offset::PlayerController::m_hController);

          if (target_controller)
          {
            // Resolve controller handle
            uintptr_t targetControllerListEntry = driver::read_memory<uintptr_t>(driverHandle, entityList + 0x8 * ((target_controller & 0x7FFF) >> 9) + 16);
            uintptr_t target_controller_ptr = driver::read_memory<uintptr_t>(driverHandle, targetControllerListEntry + 120 * (target_controller & 0x1FF));

            // Check if observing local player
            if (target_controller_ptr == localPlayer)
            {
              std::cout << "[Spectator] " << PlayerName << std::endl;
            }
          }
        }
      }
    }

    if ( !pCSPlayerPawnPtr )
      continue;

    int health = driver::read_memory<int>(GameVars::getInstance()->getDriver(), pCSPlayerPawnPtr + Offset::Pawn::CurrentHealth);
    CCSPlayerPawn.health = health;

    if (health < 1 || health > 100)
      continue;

    int armor = driver::read_memory<int>(GameVars::getInstance()->getDriver(), pCSPlayerPawnPtr + Offset::Pawn::CurrentArmor);
    CCSPlayerPawn.armor = armor;

    uintptr_t team = driver::read_memory<uintptr_t>(GameVars::getInstance()->getDriver(), pCSPlayerPawnPtr + Offset::Pawn::iTeamNum);
    CCSPlayerPawn.team = team;

    bool PlayerSpotted = driver::read_memory<bool>(GameVars::getInstance()->getDriver(), pCSPlayerPawnPtr + Offset::Pawn::entitySpottedState + Offset::Pawn::bSpottedByMask);
    CCSPlayerPawn.PlayerSpotted = PlayerSpotted;

    uint32_t PlayerFlags = driver::read_memory<uint32_t>( GameVars::getInstance()->getDriver(), pCSPlayerPawnPtr + Offset::Pawn::fFlags );
    CCSPlayerPawn.PlayerFlags   = PlayerFlags;

    uint64_t gamescene = driver::read_memory<uint64_t>( GameVars::getInstance()->getDriver(), pCSPlayerPawnPtr + Offset::Pawn::GameSceneNode );
    uint64_t bonearray = driver::read_memory<uint64_t>( GameVars::getInstance()->getDriver(), gamescene + Offset::Pawn::BoneArray + 0x80 );
    CCSPlayerPawn.BoneArray = bonearray;

    uintptr_t ActiveWeapon = driver::read_memory<uintptr_t>( GameVars::getInstance()->getDriver(), pCSPlayerPawnPtr + Offset::Pawn::pClippingWeapon );

    uint16_t  ItemDefinitionIndex = driver::read_memory<uint16_t>(
      GameVars::getInstance()->getDriver(), ActiveWeapon + Offset::EconEntity::AttributeManager
      + Offset::WeaponBaseData::Item + Offset::WeaponBaseData::ItemDefinitionIndex
    );

    CCSPlayerPawn.ItemDefinitionIndex = ItemDefinitionIndex;

    //std::cout << "PlayerSpotted: " << PlayerSpotted << std::endl;                                                             |
    //std::cout << "Offset::Pawn::pClippingWeapon:"<< Offset::Pawn::pClippingWeapon << std::endl;                               |
    //std::cout << "Offset::EconEntity::AttributeManager:"<< Offset::EconEntity::AttributeManager << std::endl;                 |
    //std::cout << "Offset::WeaponBaseData::Item:"<< Offset::WeaponBaseData::Item << std::endl;                                 | Testing
    //std::cout << "Offset::WeaponBaseData::ItemDefinitionIndex:" << Offset::WeaponBaseData::ItemDefinitionIndex << std::endl;  |
    //std::cout << ItemDefinitionIndex << std::endl;                                                                            |

    newPlayerList.push_back( CCSPlayerPawn );
  }

  /*
  // Debug: Print how many players were found
  static int debugCounter = 0;
  debugCounter++;
  if (debugCounter % 100 == 0) { // Print every 100 calls
    std::cout << "[Reader Debug] Found " << newPlayerList.size() << " valid players" << std::endl;
  }
  */

  // Atomar die Liste aktualisieren
  {
    std::lock_guard<std::mutex> lock( playerListMutex );
    playerList = std::move( newPlayerList );
  }
}

void Reader::ThreadEntitys() {
  // Wait for complete initialization
  while ( !GameVars::getInstance()->initialized() ) {
    std::this_thread::sleep_for( std::chrono::milliseconds( 100 ) );
  }

  while ( true ) {
    FilterEntitys();
    std::this_thread::sleep_for( std::chrono::milliseconds( 5 ) );
  }
}

void Reader::FilterEntitys() {
  std::vector<C_BaseEntity> newEnitityList;

  uintptr_t entity_List = driver::read_memory<uintptr_t>( GameVars::getInstance()->getDriver(), GameVars::getInstance()->getClient() + Offset::EntityList );

  // Use a fixed stride to iterate the entity list.
  for ( int i = 0; i < 2048; i++ ) {
    uintptr_t list_entry = driver::read_memory<uintptr_t>( GameVars::getInstance()->getDriver(), entity_List + 8 * ( ( i & 0x7FFF ) >> 9 ) + 0x10 );

    if ( !list_entry )
      continue;

    uintptr_t BaseEntity = driver::read_memory<uintptr_t>( GameVars::getInstance()->getDriver(), list_entry + 120 * ( i & 0x1FF ) );

    if ( !BaseEntity )
      continue;

    uintptr_t pCEntityIdentity = driver::read_memory<uintptr_t>( GameVars::getInstance()->getDriver(), BaseEntity + 0x10 );

    if ( !pCEntityIdentity )
      continue;

    uintptr_t classNameAddr = driver::read_memory<uintptr_t>( GameVars::getInstance()->getDriver(), pCEntityIdentity + 0x20 );

    if ( !classNameAddr )
      continue;

    std::string className = driver::read_string( GameVars::getInstance()->getDriver(), classNameAddr );

    if ( className.find( "_projectile" ) == std::string::npos )
      continue;

    CBaseEntity.BaseEntity = BaseEntity;
    CBaseEntity.className = className;

    newEnitityList.push_back( CBaseEntity );
  }

  // Atomar die Liste aktualisieren
  {
    std::lock_guard<std::mutex> lock( entityListMutex );
    entityList = std::move( newEnitityList );
  }
}

